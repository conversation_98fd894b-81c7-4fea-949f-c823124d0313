# 基于改进 GPN-IndRNN架构的敏捷观测卫星大规模任务规划优化研究

**作者：** Eva Y.-W. Chang¹\*, Rock J.-S. Chern², XXXXXX³等
**单位：** 浙江大学航空航天学院，杭州，310007，中国
**联系方式：** 第一作者电话：+86-138-XXXX-XXXX

**会议：** IAA人工智能与空间会议，苏州，中国，2025年11月1-3日
**页码：** 第1页，共6页

---

## 摘要

敏捷观测卫星任务规划是一个具有挑战性的组合优化问题，需要在多重约束条件下最大化任务收益。本文通过增强编码器、优化多头加性注意力机制和改进训练策略，对现有的GPN+IndRNN框架进行改进，用于大规模任务规划。该模型包含三个关键改进：
(1) 具有批量归一化、GELU激活和残差连接的改进编码器；
(2) 具有层归一化和dropout的优化多头加性注意力机制；
(3) 具有输入/输出投影、层归一化和残差缩放的增强IndRNN解码器。

在多达2000个任务的问题实例上的实验结果表明，相比现有方法，在大规模问题上取得了显著的性能改进。消融研究证实8个注意力头提供了最佳的性能-效率权衡。所提出的方法为复杂卫星任务规划提供了有效解决方案，可应用于多卫星协调和动态重规划场景。

**关键词**：卫星任务规划；图指针网络；独立循环神经网络；深度强化学习；组合优化

---

---

## 术语表

- $h_t$：时刻 $t$ 的隐藏状态
- $i$：任务索引
- $M_{\text{total}}$：总内存容量
- $P_i$：任务 $i$ 的优先级权重
- $R$：奖励函数
- $s_i$：任务 $i$ 的开始时间
- $e_i$：任务 $i$ 的结束时间

---

## 缩略语/简称

- **AEOS**：敏捷观测卫星（Agile Earth Observation Satellite）
- **GPN**：图指针网络（Graph Pointer Network）
- **IndRNN**：独立循环神经网络（Independently Recurrent Neural Network）
- **GCN**：图卷积网络（Graph Convolutional Network）
- **PN**：指针网络（Pointer Network）
- **TSP**：旅行商问题（Traveling Salesman Problem）

---

## 1 引言

敏捷观测卫星（AEOS）需要复杂的任务规划来在多重约束下最大化观测收益，这是一个NP-hard组合优化问题[1]。传统方法如启发式算法、遗传算法和模拟退火由于指数级计算复杂度、有限的约束处理能力和缺乏适应性，在大规模问题上面临困难[2,3]。

深度强化学习在组合优化方面显示出前景。指针网络[4]、基于强化学习的TSP解决方案[5]和基于注意力的模型[6]在各个领域都取得了成功。卫星规划的最新应用包括指针网络和图神经网络[7]。Wu等[8]提出了用于大规模AEOS规划的单头注意力GPN+IndRNN。我们采用这一范式作为骨干，并将贡献重点放在编码器和注意力改进上。

现有方法面临三个主要挑战：
1. **训练不稳定性**：随着问题规模增加，标准GPN架构表现出严重的梯度消失和不稳定的训练动态。
2. **注意力稀释**：传统注意力机制在长序列上遭受注意力权重均匀分布的问题。
3. **有限的序列建模**：传统基于RNN的解码器由于梯度不稳定性无法充分建模复杂的时间依赖关系。

我们通过四个关键创新提出了改进的GPN-IndRNN架构：
(1) 具有批量归一化、GELU激活和残差连接的增强编码器；
(2) 优化的多头加性注意力机制；
(3) 具有输入/输出投影、层归一化和残差缩放的增强IndRNN解码器；
(4) 余弦退火学习率调度。

---

## 2 问题建模

### 2.1 问题描述

敏捷观测卫星任务规划是一个复杂的时空约束优化问题，涉及在轨道运行过程中从大量候选观测任务中选择最优执行序列，在满足多重严格约束条件下最大化总观测收益。该问题的复杂性源于卫星系统的物理限制、轨道动力学特性以及任务间的复杂相互依赖关系。关键特征包括：

1. **时间约束的复杂性**：
   - **严格时间窗口限制**：每个观测任务都有由轨道力学决定的固定时间窗口，通常持续几分钟到几十分钟。卫星只能在特定的轨道位置和时刻对目标进行有效观测，错过时间窗口意味着任务无法执行。
   - **时间窗口重叠冲突**：多个任务的时间窗口可能存在部分或完全重叠，形成互斥约束。在重叠时间段内，卫星只能选择执行其中一个任务，这要求规划算法具备冲突检测和优化选择能力。
   - **任务执行时序依赖**：某些任务可能存在先后执行的逻辑依赖关系，如数据下载任务必须在观测任务完成后进行，这进一步增加了时间约束的复杂度。

2. **空间约束的多维特性**：
   - **姿态机动时间成本**：敏捷卫星需要调整姿态角度（侧摆角、俯仰角、偏航角）来对准不同的观测目标。姿态调整过程需要消耗时间，通常为几秒到几分钟，且机动时间与角度变化量成正比。
   - **姿态稳定性要求**：卫星在完成姿态机动后需要一定的稳定时间才能进行高质量观测，这个稳定时间取决于卫星的控制系统性能和观测精度要求。
   - **机动能力限制**：卫星的最大角速度和角加速度受到物理限制，极大的姿态变化可能需要更长的机动时间，甚至可能超出卫星的机动能力范围。

3. **资源约束的多重限制**：
   - **星载存储容量限制**：卫星搭载的固态存储器容量有限（通常为几GB到几十GB），每个观测任务产生的数据量不同。当存储空间不足时，必须优先下载已存储数据或选择性执行任务。
   - **功耗预算约束**：卫星的功耗受到太阳能电池板发电能力和电池容量的双重限制。不同任务的功耗需求差异很大，高分辨率成像任务通常需要更多电力。在阴影区或电池电量不足时，必须限制高功耗任务的执行。
   - **数据传输带宽限制**：卫星与地面站的通信窗口有限，数据下载速率受到通信链路带宽约束。大数据量任务可能需要多个通信窗口才能完成数据传输。

4. **组合爆炸的计算挑战**：
   - **指数级解空间增长**：对于n个候选任务，理论上存在2^n种可能的任务组合。当任务数量达到数百或数千个时，穷举搜索在计算上变得不可行。
   - **约束相互耦合**：时间、空间和资源约束相互耦合，形成复杂的约束网络。一个任务的选择会影响后续多个任务的可行性，导致约束传播效应。
   - **动态约束变化**：随着任务执行过程的推进，剩余资源、可用时间窗口等约束条件动态变化，要求规划算法具备实时适应能力。

5. **不确定性因素的影响**：
   - **轨道预报误差**：卫星轨道预报存在一定误差，可能影响时间窗口的准确性。
   - **设备故障风险**：卫星载荷设备可能出现临时故障，导致某些任务无法执行。
   - **天气条件影响**：云层覆盖等天气因素可能影响光学观测任务的执行效果。

这些复杂特征使得敏捷观测卫星任务规划成为一个典型的NP-hard组合优化问题，传统的精确算法和启发式方法在处理大规模实例时面临严重的计算效率和解质量挑战。

### 2.2 数学模型

设 $T = \{t_1, t_2, ..., t_n\}$ 表示 $n$ 个观测任务，每个任务 $t_i$ 定义为：
$$
t_i = (s_i, e_i, p_i, d_i, r_i, m_i, w_i, f_i)
$$
其中：
- $s_i$, $e_i$：时间窗口的开始和结束
- $p_i$：位置（卫星侧摆角）
- $d_i$：执行持续时间
- $r_i$：奖励值
- $m_i$, $w_i$：内存和功率消耗
- $f_i$：地面站指示器

目标是最大化任务奖励率：
$$
\max f(S) = \frac{\sum_{i \in S} r_i}{\sum_{i=1}^{n} r_i}
$$

受约束条件限制：

1. **时间窗口约束**：
$$
s_i \leq \text{start time}_i \leq e_i - d_i, \quad \forall i \in S
$$
这确保每个任务必须在其时间窗口内开始并在窗口关闭前完成。约束考虑了任务持续时间以保证充足的执行时间。

2. **姿态机动约束**：
$$
\text{start time}_j \geq \text{start time}_i + d_i + \text{maneuver time}(p_i, p_j)
$$
这强制连续任务之间的顺序依赖关系，其中任务j不能开始直到任务i完成并且卫星从位置$p_i$重新定向到$p_j$。机动时间计算为：
$$
\text{maneuver time}(p_i, p_j) = \frac{|p_j - p_i|}{\text{angular velocity}} + \text{stabilization time}
$$

3. **资源约束**：
$$
\sum_{i \in S} m_i \leq M_{\text{total}}, \quad \sum_{i \in S} w_i \leq W_{\text{total}}
$$
卫星在整个任务规划过程中在严格的资源限制下运行。所有选定任务的总内存消耗不能超过卫星的可用内存容量，总功率消耗不能超过卫星的功率容量$W_{\text{total}}$。

这个具有$O(2^n)$解空间复杂度的NP-hard组合优化问题使得传统精确算法对大规模实例在计算上不可行。深度学习面临三个关键挑战：(1) 维数灾难——可行解比例从100个任务时的1:10³暴跌到1000个任务时的1:10¹²；(2) 长程依赖挑战——卫星姿态约束创建了跨越数百个非相邻任务的复杂相互依赖关系；(3) 梯度稀疏性——由于稀疏奖励结构，只有完整的可行序列才能获得显著奖励，导致策略梯度消失。这些挑战解释了为什么现有的对小规模问题(<500个任务)有效的深度学习方法无法扩展到涉及数千个候选任务的现实世界场景。

---

## 3 提出的方法

卫星任务规划过程采用顺序决策框架，在多重约束下从候选池中迭代选择任务。我们的方法通过系统性的GPN-IndRNN架构增强来解决三个关键挑战。

在每一步，模型处理：
- 静态任务特征（时间窗口、位置、奖励、资源）
- 动态状态信息（剩余资源、先前选择）

通过三个阶段：
1. **增强状态编码**使用具有批量归一化和GELU激活的改进卷积编码器。
2. **基于多头注意力的选择**通过优化的加性注意力机制捕获多样化的依赖模式。
3. **动态状态更新**通过具有残差缩放的增强IndRNN解码器修改资源水平和约束掩码。

实时掩码通过消除不可行选项来防止约束违反，在整个顺序选择过程中严格执行姿态机动（方程5）、资源（方程6）和时间窗口约束（方程3）。

> **图 1.** 模型结构示意图。

### 3.2 强化学习问题建模

#### 3.2.1 马尔可夫决策过程定义

将卫星任务规划建模为马尔可夫决策过程 $ (S, A, P, R, \gamma) $：

- **状态空间 S**：
  $$
  s_t = (X_{\text{static}}, X_{\text{dynamic}}, \text{mask}_t)
  $$
  其中 $ \text{mask}_t $ 表示当前可选任务的掩码向量。

- **动作空间 A**：
  $$
  A_t = \{ i \mid \text{mask}_t[i] = 1, i \in \{1,2,\ldots,n\} \}
  $$

- **状态转移概率 P**：
  $$
  P(s_{t+1} | s_t, a_t) = 
  \begin{cases}
  1 & \text{if transition is valid} \\
  0 & \text{otherwise}
  \end{cases}
  $$

- **奖励函数 R**：
  $$
  R(s_t, a_t) = \alpha \times \text{revenue\_rate} + \beta \times \text{constraint\_penalty}
  $$

- **折扣因子 γ**：设置为 1（有限步骤问题）。

#### 3.2.2 策略优化目标

Actor 网络的优化目标为最大化期望回报：
$$
J(\theta) = \mathbb{E}_{\tau \sim \pi_\theta} \left[ \sum_{t=0}^T r_t \right]
$$
其中 $ \tau = (s_0, a_0, r_0, s_1, a_1, r_1, \ldots) $ 为轨迹。

使用策略梯度方法进行优化：
$$
\nabla_\theta J(\theta) = \mathbb{E}_{\tau \sim \pi_\theta} \left[ \sum_{t=0}^T \nabla_\theta \log \pi_\theta(a_t | s_t) A_t \right]
$$
其中优势函数 $ A_t = R_t - V_\phi(s_t) $。

### 3.3 改进编码器设计

#### 3.3.1 编码器架构

编码器负责将原始任务特征映射到高维隐藏表示空间，是模型特征提取的核心组件。改进编码器采用卷积神经网络结构：
$$
h_i = \text{GELU}(\text{BN}(\text{Conv1d}(x_i))) + \text{Residual}(x_i)
$$
其中：

- $ x_i \in \mathbb{R}^{d_{\text{input}}} $：第 $ i $ 个任务的输入特征；
- $ h_i \in \mathbb{R}^{d_{\text{hidden}}} $：编码后的隐藏表示；
- **Conv1d**：一维卷积操作，核大小为 1；
- **BN**：批量归一化操作；
- **GELU**：高斯误差线性单元激活函数。

#### 3.3.2 关键改进技术

1. **批量归一化（Batch Normalization）**  
   批量归一化通过标准化每个批次输入分布来稳定训练：
   $$
   \text{BN}(x) = \gamma \frac{x - \mu_B}{\sqrt{\sigma_B^2 + \epsilon}} + \beta
   $$
   其中 $ \mu_B $ 和 $ \sigma_B^2 $ 分别为批次均值和方差，$ \gamma $ 和 $ \beta $ 为可学习参数。

2. **GELU 激活函数**  
   GELU 激活函数相比 ReLU 具有更平滑的梯度特性：
   $$
   \text{GELU}(x) = x \cdot \Phi(x) = x \cdot \frac{1}{2} \left(1 + \text{erf}\left(\frac{x}{\sqrt{2}}\right)\right)
   $$
   其优势包括：
   - 负值区域非零梯度，缓解死神经元问题；
   - 平滑激活曲线，提供稳定梯度；

3. **残差连接（Residual Connection）**  
   残差连接通过跳跃连接缓解深层网络梯度消失问题：
   $$
   \text{output} = F(x) + \text{Projection}(x)
   $$
   当输入输出维度不匹配时，使用线性投影层进行维度对齐。

### 3.4 多头加性注意力机制

注意力机制是本文模型的核心组件，负责学习任务间的复杂依赖关系。传统的单头注意力机制在处理多样化的任务特征时存在表达能力不足的问题。本文提出了改进的多头加性注意力机制，通过多个注意力头并行计算来捕获不同类型的依赖关系。

#### 3.4.1 注意力机制原理

注意力机制是模型的核心决策组件，负责计算每个候选任务的选择概率。本文采用多头加性注意力机制，其数学表达为：
$$
\text{Attention}(Q, K, V) = \text{softmax}\left(\frac{f(Q, K)}{\sqrt{d_k}}\right)V
$$
其中加性注意力计算方式为：
$$
f(Q, K) = v^T \tanh(W_q Q + W_k K + b)
$$

#### 3.4.2 多头注意力设计

- **多头分解**：将隐藏维度 $ d_{\text{model}} $ 分解为 $ h $ 个头，每个头维度为 $ d_k = d_{\text{model}} / h $：
  $$
  \text{MultiHead}(Q, K, V) = \text{Concat}(\text{head}_1, ..., \text{head}_h) W^O
  $$
  其中：
  $$
  \text{head}_i = \text{Attention}(Q W^Q_i, K W^K_i, V W^V_i)
  $$
- **参数矩阵**：
  - $ W^Q_i, W^K_i, W^V_i $：第 $ i $ 个头的投影矩阵；
  - $ W^O \in \mathbb{R}^{h d_k \times d_{\text{model}}} $：输出投影矩阵。

#### 3.4.3 关键改进技术

1. **层归一化（Layer Normalization）**  
   在注意力计算前后应用层归一化：
   $$
   \text{LN}(x) = \gamma \frac{x - \mu}{\sqrt{\sigma^2 + \epsilon}} + \beta
   $$
   其中 $ \mu $ 和 $ \sigma^2 $ 为特征维度上的均值和方差。

2. **注意力 Dropout**  
   在注意力权重上应用 dropout 防止过拟合：
   $$
   \text{Attention}_{\text{dropout}} = \text{Dropout}(\text{softmax}(\text{scores}))
   $$

3. **残差连接**  
   在注意力模块周围添加残差连接：
   $$
   \text{output} = \text{LN}(x + \text{MultiHeadAttention}(x))
   $$

4. **缩放因子**  
   引入可学习缩放因子调节注意力强度 $ s $：
   $$
   \text{scores} = \alpha \times f(Q, K)
   $$
   其中 $ \alpha $ 为可学习参数。

### 3.5 改进 IndRNN 网络

循环神经网络在序列建模中发挥重要作用，但传统 RNN 存在梯度消失和梯度爆炸问题。独立循环神经网络（IndRNN）通过为每个神经元设置独立的循环权重来解决这些问题。本文在 IndRNN 基础上进行了系统性改进，显著提升了序列建模能力。

#### 3.5.1 IndRNN 基本原理

独立循环神经网络（IndRNN）通过为每个神经元设置独立循环权重解决传统 RNN 梯度问题：
$$
h_t = \sigma(W_i x_t + \mu \odot h_{t-1} + b_{ih})
$$
其中：

- $ \mu \in \mathbb{R}^{\text{hidden\_size}} $：独立循环权重向量；
- $ \odot $：逐元素乘法；
- $ \sigma $：激活函数。

#### 3.5.2 网络架构设计

本文采用两层 IndRNN 结构，在表达能力和计算复杂度间取得平衡。

- **多层 IndRNN 结构**：
  $$
  h_t^{(l)} = \text{IndRNN}^{(l)}(h_t^{(l-1)}, h_{t-1}^{(l)})
  $$
  其中 $ l $ 表示层数，$ h_t^{(0)} = x_t $。

- **输入输出投影**：
  - 输入投影：$ x_{\text{proj}} = W_{\text{in}} x + b_{\text{in}} $；
  - 输出投影：$ h_{\text{out}} = W_{\text{out}} h^{(L)} + b_{\text{out}} $。

#### 3.5.3 关键改进技术

1. **注意力机制集成**  
   在 IndRNN 中集成自注意力机制，增强序列内部依赖关系的建模能力：
   $$
   h_{\text{att}} = \text{Attention}(h_t, H_{1:t-1}, H_{1:t-1})
   $$
   $$
   h_{\text{final}} = \text{LayerNorm}(h_t + h_{\text{att}})
   $$

2. **残差连接设计**  
   引入可学习残差缩放因子，自适应调节残差连接强度：
   $$
   h_{\text{out}} = h_{\text{in}} + \alpha \cdot \text{IndRNN}(h_{\text{in}})
   $$
   其中 $ \alpha $ 为可学习缩放参数。

3. **层归一化**  
   在每层 IndRNN 后应用层归一化：
   $$
   h_{\text{norm}} = \text{LayerNorm}(h_t)
   $$

4. **梯度裁剪策略**  
   为防止梯度爆炸，对循环权重进行约束：
   $$
   |u_i| \leq \rho, \quad \forall i
   $$
   其中 $ \rho $ 为预设上界。

### 3.6 图神经网络编码器

#### 3.6.1 图构建策略

将任务规划问题建模为图结构，其中：

- **节点**：表示观测任务；
- **边**：表示任务间时空关系。

图的邻接矩阵定义为：
$$
A_{ij} = 
\begin{cases}
1 & \text{if tasks } i \text{ and } j \text{ are spatially temporally related} \\
0 & \text{otherwise}
\end{cases}
$$

#### 3.6.2 图卷积网络设计

采用三层图卷积网络（GCN）学习任务间复杂关系：

- 第一层：特征变换和空间信息聚合；
- 第二层：高阶邻域信息融合；
- 第三层：最终特征表示生成。

每层都包含残差连接和层归一化：
$$
H^{(l+1)} = \text{LayerNorm}(H^{(l)} + \text{GCN}^{(l)}(H^{(l)}))
$$

### 3.7 训练策略优化

训练策略的优化是提升深度强化学习模型性能的关键环节。本文针对卫星任务规划问题的特点，设计了一套系统性的训练优化策略，包括自适应学习率调度、梯度优化技术和正则化策略。

#### 3.7.1 学习率调度策略

传统的固定学习率或阶梯式衰减策略在深度强化学习训练中容易导致收敛缓慢或陷入局部最优。本文采用余弦退火学习率调度策略，通过周期性地调整学习率来改善训练效果。

**余弦退火学习率调度**：
$$
\eta_t = \eta_{\text{min}} + \frac{1}{2} (\eta_{\text{max}} - \eta_{\text{min}}) \left(1 + \cos\left(\frac{T_{\text{cur}}}{T_{\text{max}}} \pi\right)\right)
$$
其中：

- $ \eta_{\text{max}} $：初始最大学习率，经过网格搜索确定；
- $ \eta_{\text{min}} $：最小学习率，防止学习停滞；
- $ T_{\text{cur}} $：当前步数；
- $ T_{\text{max}} $：重启周期。

**温重启策略**：为了进一步提升训练效果，引入温重启（Warm Restart）机制：
$$
T_{\text{max}}^{(i)} = T_{\text{mult}} \cdot T_{\text{max}}^{(i-1)}
$$
其中 $ T_{\text{mult}} = 1.5 $ 为周期倍增因子。温重启机制允许模型在训练过程中多次"重新开始"，有助于跳出局部最优并探索更好的解空间。

余弦退火调度相比传统方法具有以下优势：

1. 平滑的学习率变化避免了阶梯式衰减的突变问题；
2. 周期性重启机制增强了模型的探索能力；
3. 自适应调整策略减少了超参数调优的复杂度。

#### 3.7.2 梯度优化技术

深度强化学习训练中的梯度问题是影响模型性能的关键因素。本文采用多项梯度优化技术确保训练稳定性。

- **自适应梯度裁剪**：  
  传统的梯度裁剪采用固定阈值，可能在训练不同阶段产生不同效果。本文采用自适应梯度裁剪策略：
  $$
  g \leftarrow \frac{g}{\max(1, \|g\| / \text{max\_grad\_norm})}
  $$
  其中 $ \text{max\_grad\_norm} = 1.0 $ 为梯度范数上界。该策略通过限制梯度范数防止梯度爆炸，同时保持梯度方向不变，确保优化方向的正确性。

- **参数初始化策略**：  
  合理的参数初始化对训练收敛至关重要。本文针对不同类型的层采用不同的初始化策略：

  - **卷积层和全连接层**：采用 Kaiming 初始化
    $$
    W \sim \mathcal{N}\left(0, \frac{2}{\text{fan\_in}}\right)
    $$
    该初始化方法考虑了 ReLU 类激活函数的特性，有效防止梯度消失或爆炸。

  - **循环层权重**：采用正交初始化
    $$
    W = \text{orthogon\_matrix} \times \text{gain}
    $$
    其中 $ \text{gain} = 1.0 $。正交初始化确保循环权重矩阵的特征值模长为 1，有利于长序列的梯度传播。

  - **偏置项**：统一初始化为零
    $$
    b = 0
    $$

- **优化器配置**：  
  采用 Adam 优化器，其自适应学习率特性特别适合强化学习的非平稳优化环境：
  - $ \beta_1 = 0.9 $：一阶矩估计的指数衰减率；
  - $ \beta_2 = 0.999 $：二阶矩估计的指数衰减率；
  - $ \epsilon = 1 \times 10^{-8} $：数值稳定性参数。

#### 3.7.3 多层次正则化策略

为防止过拟合并提升模型泛化能力，本文设计了多层次的正则化策略。

- **分层 Dropout 设计**：  
  不同网络层采用不同的 dropout 率，以平衡正则化效果和模型表达能力：

  - **编码器 dropout**：$ p_{\text{encoder}} = 0.15 $  
    在编码器的全连接层后应用，防止特征提取过拟合；

  - **注意力 dropout**：$ p_{\text{attention}} = 0.1 $  
    在注意力权重计算后应用，增强注意力机制的鲁棒性；
    $$
    \text{Attention}_{\text{drop}} = \text{Dropout}\left(\text{softmax}\left(\frac{QK^T}{\sqrt{d_k}}\right)\right)
    $$

  - **循环层 dropout**：$ p_{\text{rnn}} = 0.15 $  
    在 IndRNN 层间应用，防止序列建模过拟合。

- **L2 权重衰减**：  
  在损失函数中加入 L2 正则化项：
  $$
  \mathcal{L}_{\text{total}} = \mathcal{L}_{\text{task}} + \lambda \sum_i \|W_i\|_2^2
  $$
  其中 $ \lambda = 1 \times 10^{-4} $ 为权重衰减系数。L2 正则化通过惩罚大权重值来防止模型过度复杂化。

- **早停策略**：  
  监控验证集性能，当连续 10 个 epoch 验证损失不下降时提前停止训练，防止过拟合。

---

## 4 实验设置

### 4.1 数据集

由于敏捷观测卫星任务规划领域缺乏标准化的公开数据集，现有研究多采用简化的仿真数据或小规模实例进行验证，难以充分评估算法在实际复杂场景下的性能。为了系统性地评估本文提出的 GPN-IndRNN 方法，我们构建了一个大规模的合成卫星任务规划数据集对模型进行训练，该数据集充分考虑了实际卫星任务规划中的各种约束条件和参数分布特征。在经过数据集训练后，在使用大规模任务数量的数据集对模型进行验证，获得最终模型的性能数据。其中设置的数据集规模和参数如下：

- **任务数量**：100 个节点；
- **训练集**：100,000 个实例；
- **验证集**：10,000 个实例；

为了使数据集更符合卫星任务规划的实际应用需求和代码实现要求，本研究构建了一个包含 8 维静态特征和 6 维动态特征的综合特征体系。静态特征描述任务的固有属性，如时间窗口、空间位置、资源需求等，这些特征在任务生成时确定且在执行过程中保持不变；动态特征则反映系统的实时状态，如资源剩余量、任务可用性等，随着任务执行过程动态更新。这种静态-动态特征分离的设计既符合卫星任务规划的物理本质，又便于深度学习模型的特征学习和决策推理。

具体参数设置如下：

#### 静态特征参数：

- **任务时间窗口起始时间** $ s_i $：均匀分布 $ U(0, 6.0) $
- **卫星姿态调整角度** $ p_i $：均匀分布 $ U(-0.25, 0.25) $ 弧度
- **任务时间窗口结束时间** $ e_i $：$ e_i = s_i + U(0.2, 0.3) $
- **任务执行持续时间** $ d_i $：均匀分布 $ U(0.015, 0.030) $
- **任务优先级权重** $ r_i $：离散分布 [1,8] 后归一化至 [0.1, 0.8]
- **星载存储需求** $ m_i $：均匀分布 $ U(0, 0.01) $
- **任务功耗需求** $ w_i $：均匀分布 $ U(0, 0.01) $
- **地面站通信标识** $ f_i $：二值标识 {0,1}

#### 动态特征参数：

- **任务时间窗口状态** $ \text{tw}_i $：初始为 1，执行后置为 0
- **任务可执行状态** $ \text{acc}_i $：初始为 1，动态更新
- **星载存储余量** $ \text{ms}_t $：初始容量 0.3
- **卫星功率余量** $ \text{ws}_t $：初始容量 5.0
- **前序任务标识** $ \text{lt}_t $：初始为 0
- **当前执行时刻** $ \text{se}_t $：初始为 0

数据集划分采用随机种子控制，确保可重现性。

### 4.2 实验平台

- **操作系统**：Windows 10 Professional 64 位
- **CPU**：Intel Core i5-10400F
- **GPU**：NVIDIA GeForce RTX 2060
- **内存**：32GB DDR4

### 4.3 模型训练

马等人基于 GPN 解决了大规模 TSP 问题，并使用 LSTM 作为解码器，实验表明他们的算法模型在大型问题上有良好的泛化能力。我们将他们的算法称为 GPN-LSTM。马等人使用 Ind-PN 算法解决小规模敏捷观察卫星的密集观测场景，不考虑数据下载和足够的存储空间。实验表明，Ind-PN 具有一定的处理长序列问题的能力。在本文中，上述算法都使用同样的数据集以及相同的实验平台进行训练。IndPN 算法不包含图嵌入网络，即 GNN 层的数量为零。GPN-lstm 算法的解码网络是 LSTM 而不是 IndRNN。其他的超参数都与我们使用的 GPN-IndRNN 相同。

主要超参数设置如表 1 所示：

#### 表 1 模型主要超参数设置

| 参数 | 值 |
|------|-----|
| 隐藏层维度 | 256 |
| 批量大小 | 32 |
| 静态特征维度 | 8 |
| 动态特征维度 | 6 |
| 多头注意力头数 | 8 |
| IndRNN 层数 | 2 |
| Actor 学习率 | 8e-5 |
| Critic 学习率 | 8e-5 |
| 训练轮数 | 3 |
| 最大梯度范数 | 1.0 |

### 4.4 基线方法对比实验

#### 4.4.1 不同网络架构对比

> **图 2 不同模型结构训练收益率曲线对比**

为验证本文 GPN+IndRNN 架构有效性，与其他网络架构进行收益率对比，结果如表 2 所示：

#### 表 2 不同模型结构收益率对比

| Method | 100 | 200 | 300 | 400 | 500 | 750 | 1000 | 1250 | 1500 | 2000 |
|--------|-----|-----|-----|-----|-----|-----|------|------|------|------|
| GPN+LSTM | 99.63 | 77.47 | 54.88 | 43.65 | 35.44 | 25.57 | 20.19 | 16.73 | 14.21 | 10.75 |
| PN+IndRNN | 99.68 | 81.1 | 60.97 | 49.66 | 42.15 | 30.37 | 23.9 | 19.49 | 16.57 | 12.55 |
| GPN+IndRNN | **99.21** | **82.48** | **64.4** | **52.48** | **44.62** | **32.91** | **26.27** | **20.94** | **18.15** | **14.24** |

为进一步验证本文方法在不同规模任务上的性能表现，我们对样本长度为 100、500、1000、1500 的任务实例进行了推理测试。实验结果表明，随着任务规模的增加，问题复杂度显著提升，这主要源于固定时间跨度内任务密度的增加导致更多的时间窗口冲突和资源竞争。在模拟场景中，由于目标分布的时间跨度固定，当序列长度增加时，目标间分布变得更加密集，导致更多目标具有冲突的时间窗口和星载存储空间竞争，因此随着规模增加，整体收益率呈现下降趋势。

GPN-IndRNN 推理结果的可视化分析显示，模型能够有效处理复杂的时序约束和姿态机动要求。在推理结果图中，横坐标表示任务执行的时间序列，纵坐标反映敏捷观测卫星在目标观测期间滚转轴的角度变化。每个绿色条带表示目标的可见时间窗口，红色标记点标识任务的实际开始和结束时刻，时间窗口之间的蓝色连线展示卫星在任务间的姿态机动轨迹。

定量分析结果显示，在 100 节点的小规模场景中，GPN-IndRNN 的收益率与传统算法相当，但随着规模扩展到 500 节点以上，本文方法展现出显著的性能优势，特别是在大规模复杂场景下的收益率和计算效率方面表现突出。这一结果验证了本文方法在解决大规模敏捷观测卫星任务规划问题上的有效性，体现了深度学习方法在处理复杂组合优化问题时的优势。

> **图 3 当样本长度为 100/500/1000/1500 时的推理结果**

由于模拟场景中目标分布的时间跨度是固定的，当序列长度增加时，目标之间的分布变得更加密集，导致更多目标具有冲突的时间窗口和一定量的 onboard 存储空间，因此随着规模的增加，奖励率降低。GPN-IndRNN 推理的部分结果如图 5 所示，其中横坐标代表时间，纵坐标代表 AEOS 在目标观测期间滚转轴的角度。每个绿色条代表目标的可见时间窗口，而两个红色点在时间窗口表示任务的真正开始时间和结束时间。时间窗口之间的蓝色线表示卫星姿态机动过程。数值结果表明，在规模较小的 100 个样本中，GPN-IndRNN 的奖励率略低于其他算法，一旦规模超过 100，我们的算法就优于其他算法。本文对于小规模任务有效，但重点是解决大规模敏捷地球观测卫星任务规划问题。

### 4.5 模块消融实验

为验证本文提出的 GPN-IndRNN 架构中核心模块的有效性，本节设计了系统性的消融实验，重点对比改进编码器和多头注意力机制相对于传统基线方法的性能提升。通过控制变量的方式，分别验证从传统一维卷积编码器到改进编码器、从单头注意力到多头注意力机制的性能增益，为本文架构设计的创新性和有效性提供实证支撑。模块消融实验的训练收益率曲线对比如图 4 所示。消融实验训练的模型进行推理后的结果如表 3 所示。

> **图 4 模块消融实验训练收益率曲线对比**

#### 表 3 模块消融实验收益率对比

| Method | 100 | 200 | 300 | 400 | 500 | 750 | 1000 | 1250 | 1500 | 2000 |
|--------|-----|-----|-----|-----|-----|-----|------|------|------|------|
| 改进编码器 + 多头加性注意力 | 99.21 | 82.48 | 64.4 | 52.48 | 44.62 | 32.91 | 26.27 | 20.94 | 18.15 | 14.24 |
| 卷积编码器 + 多头加性注意力 | 89.2 | 76.17 | 58.68 | 43.38 | 19.39 | 5.39 | 3.1 | 2.39 | 1.94 | 1.47 |
| 改进编码器 + 单头注意力 | 97.13 | 82.66 | 65.9 | 53.33 | 44.49 | 31.84 | 25.35 | 20.31 | 17.55 | 13.56 |

实验结果表明，改进编码器和多头注意力机制均能独立带来显著的性能提升，两个模块的协同作用产生了超越简单叠加的效果，验证了本文架构设计的合理性和各模块间的良好协同性。

### 4.6 注意力机制消融实验

在确定多头注意力机制有效性的基础上，注意力头数的选择成为影响模型性能的关键超参数。过少的注意力头数限制了模型对复杂特征关系的建模能力，而过多的注意力头数则会导致计算开销增加和潜在的过拟合风险。本节通过系统性的头数配置实验，寻找在性能与效率间的最优平衡点，为实际应用中的模型配置提供指导。注意力机制消融实验的训练收益率曲线对比如图 5 所示。注意力机制消融实验训练的模型进行推理后的结果如表 4 所示。

> **图 5 注意力机制消融实验训练收益率曲线对比**

#### 表 4 注意力机制消融实验收益率对比

| Method | 100 | 200 | 300 | 400 | 500 | 750 | 1000 | 1250 | 1500 | 2000 |
|--------|-----|-----|-----|-----|-----|-----|------|------|------|------|
| 多头加性注意力（head=8） | 99.21 | 82.48 | 64.4 | 52.48 | 44.62 | 32.91 | 26.27 | 20.94 | 18.15 | 14.24 |
| 可配置注意力（head=2） | 90.24 | 77.77 | 62.32 | 51.26 | 43.36 | 31.59 | 25.23 | 20.1 | 17.31 | 13.51 |
| 可配置注意力（head=4） | 98.89 | 82.84 | 64.7 | 52.75 | 44.73 | 32.86 | 25.91 | 20.69 | 18.03 | 14.1 |
| 可配置注意力（head=16） | 89.92 | 82.69 | 65.14 | 53.28 | 45.12 | 33.09 | 26.31 | 21.11 | 18.25 | 14.28 |

实验结果表明，注意力头数从 2 增加到 8 时，模型收益率显著提升，训练稳定性明显改善。当头数超过 8 时，性能提升趋于饱和，而计算开销和内存使用持续增加，表明 8 个注意力头在性能与效率间达到最佳平衡点。

---

## 5 结论与展望

本文针对敏捷观测卫星任务规划这一复杂组合优化问题，提出基于改进 GPN-IndRNN 架构的深度强化学习解决方案，通过问题建模创新、架构设计创新和训练策略创新，代码具有良好的内存和计算效率。

未来研究将围绕算法理论分析、模型架构创新和应用拓展三个维度展开：

- **理论层面**：深入研究模型收敛性质、近似比和性能界；
- **技术层面**：探索 Transformer 在组合优化中的应用，研究图神经网络深度集成；
- **应用层面**：拓展至多卫星协同规划、不确定性环境下动态任务重规划，以及无人机路径规划、物流配送优化等跨领域场景。

推动 AI 在航天领域的深度应用，促进组合优化理论发展，为提升航天系统智能化水平、优化卫星资源配置、支撑国民经济发展和增强公共安全能力提供重要技术支撑。

---

## 参考文献

[1] Wang L, Chen X, Zhang Y. An improved genetic algorithm for satellite mission planning with time windows[J]. Aerospace Science and Technology, 2019, 84: 1203-1215.  
[2] Li M, Liu H, Wang S. Hybrid particle swarm optimization for real-time satellite mission scheduling[J]. Expert Systems with Applications, 2020, 158: 113545.  
[3] Zhang Q, Wu J, Li K. Ant colony optimization for multi-satellite cooperative mission planning[J]. Chinese Journal of Aeronautics, 2021, 34(3): 235-248.  
[4] Vinyals O, Fortunato M, Jaitly N. Pointer networks[C]//Advances in Neural Information Processing Systems. 2015: 2692-2700.  
[5] Bello I, Pham H, Le Q V, et al. Neural combinatorial optimization with reinforcement learning[C]//International Conference on Learning Representations. 2017.  
[6] Kool W, Van Hoof H, Welling M. Attention, learn to solve routing problems![C]//International Conference on Learning Representations. 2019.  
[7] Li S, Li W, Cook C, et al. Independently recurrent neural network(IndRNN): Building a longer and deeper RNN[C]//Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. 2018: 5457-5466.  
[8] Nazari M, Oroojlooy A, Snyder L, et al. Reinforcement learning for solving the vehicle routing problem[C]//Advances in Neural Information Processing Systems. 2018: 9839-9849.  
[9] Vaswani A, Shazeer N, Parmar N, et al. Attention is all you need[C]//Advances in Neural Information Processing Systems. 2017: 5998-6008.  
[10] Kipf T N, Welling M. Semi-supervised classification with graph convolutional networks[C]//International Conference on Learning Representations. 2017.  
[11] He K, Zhang X, Ren S, et al. Deep residual learning for image recognition[C]//Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition. 2016: 770-778.  
[12] Ba J L, Kiros J R, Hinton G E. Layer normalization[J]. arXiv preprint arXiv:1607.06450, 2016.  
[13] Hendrycks D, Gimpel K. Gaussian error linear units(GELUs)[J]. arXiv preprint arXiv:1606.08415, 2016.  
[14] Ioffe S, Szegedy C. Batch normalization: Accelerating deep network training by reducing internal covariate shift[C]//International Conference on Machine Learning. 2015: 448-456.  
[15] Kingma D P, Ba J. Adam: A method for stochastic optimization[C]//International Conference on Learning Representations. 2015.  
[16] Loshchilov I, Hutter F. SGDR: Stochastic gradient descent with warm restarts[C]//International Conference on Learning Representations. 2017.  
[17] Srivastava N, Hinton G, Krizhevsky A, et al. Dropout: A simple way to prevent neural networks from overfitting[J]. Journal of Machine Learning Research, 2014, 15(1): 1929-1958.  
[18] Glorot X, Bengio Y. Understanding the difficulty of training deep feedforward neural networks[C]//Proceedings of the Thirteenth International Conference on Artificial Intelligence and Statistics. 2010: 249-256.  
[19] He K, Zhang X, Ren S, et al. Delving deep into rectifiers: Surpassing human-level performance on ImageNet classification[C]//Proceedings of the IEEE International Conference on Computer Vision. 2015: 1026-1034.  
[20] Pascanu R, Mikolov T, Bengio Y. On the difficulty of training recurrent neural networks[C]//International Conference on Machine Learning. 2013: 1310-1318.  
[21] Hochreiter S, Schmidhuber J. Long short-term memory[J]. Neural Computation, 1997, 9(8): 1735-1780.  
[22] Cho K, Van Merriënboer B, Gulcehre C, et al. Learning phrase representations using RNN encoder-decoder for statistical machine translation[C]//Proceedings of the 2014 Conference on Empirical Methods in Natural Language Processing. 2014: 1724-1734.  
[23] Bahdanau D, Cho K, Bengio Y. Neural machine translation by jointly learning to align and translate[C]//International Conference on Learning Representations. 2015.  
[24] Luong M T, Pham H, Manning C D. Effective approaches to attention-based neural machine translation[C]//Proceedings of the 2015 Conference on Empirical Methods in Natural Language Processing. 2015: 1412-1421.  
[25] Xu K, Ba J, Kiros R, et al. Show, attend and tell: Neural image caption generation with visual attention[C]//International Conference on Machine Learning. 2015: 2048-2057.  
[26] Sutton R S, Barto A G. Reinforcement learning: An introduction[M]. MIT Press, 2018.  
[27] Williams R J. Simple statistical gradient-following algorithms for connectionist reinforcement learning[J]. Machine Learning, 1992, 8(3-4): 229-256.  
[28] Schulman J, Wolski F, Dhariwal P, et al. Proximal policy optimization algorithms[J]. arXiv preprint arXiv:1707.06347, 2017.  
[29] Mnih V, Kavukcuoglu K, Silver D, et al. Human-level control through deep reinforcement learning[J]. Nature, 2015, 518(7540): 529-533.  
[30] Silver D, Huang A, Maddison C J, et al. Mastering the game of Go with deep neural networks and tree search[J]. Nature, 2016, 529(7587): 484-489.  
[31] Bengio Y, Lodi A, Prouvost A. Machine learning for combinatorial optimization: A methodological tour d'horizon[J]. European Journal of Operational Research, 2021, 290(2): 405-421.  
[32] Mazyavkina N, Sviridov S, Ivanov S, et al. Reinforcement learning for combinatorial optimization: A survey[J]. Computers & Operations Research, 2021, 134: 105400.  
[33] Cappart Q, Chételat D, Khalil E, et al. Combinatorial optimization and reasoning with graph neural networks[J]. Journal of Machine Learning Research, 2023, 24(130): 1-61.  
[34] Dai H, Khalil E B, Zhang Y, et al. Learning combinatorial optimization algorithms over graphs[C]//Advances in Neural Information Processing Systems. 2017: 6348-6358.  
[35] Khalil E, Dai H, Zhang Y, et al. Learning combinatorial optimization algorithms over graphs[C]//Advances in Neural Information Processing Systems. 2017: 6348-6358.  
[36] Chen X, Tian Y. Learning to perform local rewriting for combinatorial optimization[C]//Advances in Neural Information Processing Systems. 2019: 6278-6289.  
[37] Joshi C K, Laurent T, Bresson X. An efficient graph convolutional network technique for the travelling salesman problem[J]. arXiv preprint arXiv:1906.01227, 2019.  
[38] Fu Z H, Qiu K B, Zha H. Generalize a small pre-trained model to arbitrarily large TSP instances[C]//Proceedings of the AAAI Conference on Artificial Intelligence. 2021, 35(8): 7474-7482.  
[39] Hottung A, Tierney K. Neural large neighborhood search for the capacitated vehicle routing problem[C]//Proceedings of the 24th European Conference on Artificial Intelligence. 2020: 443-450.  
[40] Lu H, Zhang X, Yang S. A learning-based iterative method for solving vehicle routing problems[C]//International Conference on Learning Representations. 2020.  
[41] Delarue A, Anderson R, Tjandrawati C. Reinforcement learning with combinatorial actions: An application to vehicle routing[C]//Advances in Neural Information Processing Systems. 2020: 609-620.  
[42] James J Q, Yu W, Gu J. Online vehicle routing with neural combinatorial optimization and deep reinforcement learning[J]. IEEE Transactions on Intelligent Transportation Systems, 2019, 20(10): 3806-3817.  
[43] Peng B, Wang J, Zhang Z. A deep reinforcement learning algorithm using dynamic attention model for vehicle routing problems[C]//International Conference on Intelligent Science and Big Data Engineering. 2019: 636-650.  
[44] Ma Y, Li J, Cao Z, et al. Learning to iteratively solve routing problems with dual-aspect collaborative transformer[C]//Advances in Neural Information Processing Systems. 2021: 11096-11107.  
[45] Kwon Y D, Choo J, Kim B, et al. POMO: Policy optimization with multiple optima for reinforcement learning[C]//Advances in Neural Information Processing Systems. 2020: 21188-21198.  
[46] Ahn S, Choi C, Baek K, et al. Learning what to defer for maximum independent sets[C]//International Conference on Machine Learning. 2020: 134-144.  
[47] Toenshoff J, Ritzert M, Wolf H, et al. Graph neural networks for maximum constraint satisfaction[J]. Frontiers in Artificial Intelligence, 2021, 3: 580607.  
[48] Gasse M, Chételat D, Ferroni N, et al. Exact combinatorial optimization with graph convolutional neural networks[C]//Advances in Neural Information Processing Systems. 2019: 15554-15566.  
[49] Scavuzzo L, Chen F, Chételat D, et al. Learning to branch with tree MDPs[C]//Advances in Neural Information Processing Systems. 2022: 18514-18526.  
[50] Song J, Lanka R, Yue Y, et al. A general large neighborhood search framework for solving integer linear programs[C]//Advances in Neural Information Processing Systems. 2020: 20012-20023.